'use strict';

/**
 * Demand Forecast Controller - PRD Implementation
 * 
 * Handles demand forecasting operations according to PRD specifications:
 * - Forecast generation and management
 * - Historical analysis
 * - Accuracy tracking
 * - Integration with scheduling
 */

const demandForecastService = require('../../services/rota/demandForecastService');
const instanceForecastingService = require('../../services/rota/instanceForecastingService');
const { successResponse } = require('../../common/utils/response');
const { ValidationError, NotFoundError } = require('../../common/errors');
const fs = require('fs');
const XLSX = require('xlsx');
/**
 * Helper function to calculate forecast type distribution
 * @param {Array} forecasts - Array of forecasts
 * @returns {Object} Distribution of forecast types
 */
const calculateForecastTypeDistribution = (forecasts) => {
  const distribution = {};
  forecasts.forEach(forecast => {
    const type = forecast.forecastType || 'unknown';
    distribution[type] = (distribution[type] || 0) + 1;
  });
  return distribution;
};

/**
 * Generate demand forecasts
 * @route POST /api/v1/demand-forecasts/generate
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const generateForecasts = async (req, res, next) => {
  try {
    const {
      departmentId,
      startDate,
      endDate,
      forecastType = 'historical_average',
      historicalPeriodDays = 30,
      includeSeasonality = true,
      includeEvents = false
    } = req.body;

    const forecastRequest = {
      departmentId,
      startDate,
      endDate,
      forecastType,
      historicalPeriodDays,
      includeSeasonality,
      includeEvents
    };

    const result = await demandForecastService.generateDemandForecasts(forecastRequest, req.tenantContext);

    return successResponse(res, {
      message: 'Demand forecasts generated successfully',
      data: result
    }, 201);
  } catch (error) {
    next(error);
  }
};

/**
 * Get active forecasts
 * @route GET /api/v1/demand-forecasts
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getActiveForecasts = async (req, res, next) => {
  try {
    const {
      departmentId,
      startDate,
      endDate,
      minConfidence = 0,
      forecastTypes,
      page = 1,
      limit = 50
    } = req.query;

    const filters = {
      departmentId: departmentId ? parseInt(departmentId) : null,
      startDate,
      endDate,
      minConfidence: parseFloat(minConfidence),
      forecastTypes: forecastTypes ? forecastTypes.split(',') : []
    };

    const forecasts = await demandForecastService.getActiveForecasts(filters, req.tenantContext);

    // Apply pagination
    const offset = (page - 1) * limit;
    const paginatedForecasts = forecasts.slice(offset, offset + limit);

    return successResponse(res, {
      message: 'Active forecasts retrieved successfully',
      data: paginatedForecasts,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: forecasts.length,
        totalPages: Math.ceil(forecasts.length / limit),
        hasNext: page < Math.ceil(forecasts.length / limit),
        hasPrev: page > 1
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update forecast accuracy
 * @route PUT /api/v1/demand-forecasts/:id/accuracy
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const updateForecastAccuracy = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { actualRequirement } = req.body;

    const updatedForecast = await demandForecastService.updateForecastAccuracy(
      id,
      actualRequirement,
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Forecast accuracy updated successfully',
      data: updatedForecast
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get forecast accuracy statistics
 * @route GET /api/v1/demand-forecasts/accuracy-stats
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getForecastAccuracyStats = async (req, res, next) => {
  try {
    const {
      departmentId,
      startDate,
      endDate,
      forecastType
    } = req.query;

    const filters = {
      departmentId: departmentId ? parseInt(departmentId) : null,
      startDate,
      endDate,
      forecastType
    };

    const stats = await demandForecastService.getForecastAccuracyStats(filters, req.tenantContext);

    return successResponse(res, {
      message: 'Forecast accuracy statistics retrieved successfully',
      data: stats
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get forecast recommendations for instances
 * @route POST /api/v1/demand-forecasts/recommendations
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getForecastRecommendations = async (req, res, next) => {
  try {
    const { instanceIds } = req.body;

    if (!instanceIds || !Array.isArray(instanceIds) || instanceIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Instance IDs array is required'
      });
    }

    const recommendations = await instanceForecastingService.getForecastRecommendations(
      instanceIds,
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Forecast recommendations retrieved successfully',
      data: recommendations
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Apply forecasts to instances
 * @route POST /api/v1/demand-forecasts/apply-to-instances
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const applyForecastsToInstances = async (req, res, next) => {
  try {
    const {
      instanceIds,
      minConfidenceThreshold = 70,
      onlyIfHigherConfidence = true,
      preserveManualOverrides = true
    } = req.body;

    if (!instanceIds || !Array.isArray(instanceIds) || instanceIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Instance IDs array is required'
      });
    }

    const updateOptions = {
      minConfidenceThreshold,
      onlyIfHigherConfidence,
      preserveManualOverrides
    };

    const result = await instanceForecastingService.updateInstancesWithLatestForecasts(
      instanceIds,
      updateOptions,
      req.tenantContext
    );

    return successResponse(res, {
      message: `Forecast application completed: ${result.updated} updated, ${result.skipped} skipped`,
      data: result
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get forecast dashboard data
 * @route GET /api/v1/demand-forecasts/dashboard
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getForecastDashboard = async (req, res, next) => {
  try {
    const {
      departmentId,
      period = '30d'
    } = req.query;

    const daysBack = parseInt(period.replace('d', '')) || 30;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - daysBack);
    const endDate = new Date();

    // Get active forecasts
    const activeForecasts = await demandForecastService.getActiveForecasts({
      departmentId: departmentId ? parseInt(departmentId) : null,
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0]
    }, req.tenantContext);

    // Get accuracy stats
    const accuracyStats = await demandForecastService.getForecastAccuracyStats({
      departmentId: departmentId ? parseInt(departmentId) : null,
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0]
    }, req.tenantContext);

    // Calculate summary metrics
    const summary = {
      totalActiveForecasts: activeForecasts.length,
      averageConfidence: activeForecasts.length > 0 ?
        Math.round(activeForecasts.reduce((sum, f) => sum + parseFloat(f.confidence), 0) / activeForecasts.length) : 0,
      forecastTypeDistribution: calculateForecastTypeDistribution(activeForecasts),
      upcomingForecasts: activeForecasts.filter(f => new Date(f.date) > new Date()).length
    };

    return successResponse(res, {
      message: 'Forecast dashboard data retrieved successfully',
      data: {
        summary,
        activeForecasts: activeForecasts.slice(0, 10), // Latest 10
        accuracyStats,
        period: { period, startDate, endDate }
      }
    });
  } catch (error) {
    next(error);
  }
};

// ==================== BASIC CRUD OPERATIONS ====================

/**
 * Create a new demand forecast
 * @route POST /api/v1/demand-forecasts
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const createForecast = async (req, res, next) => {
  try {
    // ✅ CHECK IF EXCEL FILE IS UPLOADED
    if (req.file) {
      // EXCEL UPLOAD MODE - Your multer config saves files to dis

      // Parse Excel file from disk (your multer saves to uploads directory)
      const workbook = XLSX.readFile(req.file.path);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];

      // Convert to JSON
      const excelData = XLSX.utils.sheet_to_json(worksheet);

      if (excelData.length === 0) {
        // Clean up file before returning error
        if (fs.existsSync(req.file.path)) {
          fs.unlinkSync(req.file.path);
        }
        return res.status(400).json({
          success: false,
          message: 'Excel file is empty or has no valid data'
        });
      }

      // Process Excel data
      const results = await demandForecastService.processExcelUpload(excelData, req.tenantContext);

      // Add file info to results
      results.fileInfo = {
        originalName: req.file.originalname,
        processedAt: new Date().toISOString(),
        totalRows: excelData.length,
        fileSize: req.file.size,
        uploadPath: req.file.path
      };

      try {
        if (fs.existsSync(req.file.path)) {
          fs.unlinkSync(req.file.path);
          console.log(`✅ Excel file deleted: ${req.file.path}`);
        }
      } catch (deleteError) {
        console.error(`❌ Failed to delete Excel file: ${req.file.path}`, deleteError);
      }

      return successResponse(res, {
        message: `Excel processing completed. ${results.successful} successful, ${results.failed} failed. File processed and removed.`,
        data: results
      }, 201);

    } else {
      // ✅ MANUAL CREATION MODE
      const { forecasts } = req.body;

      if (forecasts && Array.isArray(forecasts)) {
        // BULK MANUAL CREATION
        const results = {
          total: forecasts.length,
          successful: 0,
          failed: 0,
          errors: [],
          created: []
        };

        // Process each forecast manually
        for (let i = 0; i < forecasts.length; i++) {
          try {
            const forecast = await demandForecastService.createForecast(forecasts[i], req.tenantContext);
            results.created.push({
              id: forecast.id,
              date: forecast.date,
              departmentId: forecast.departmentId,
              designationId: forecast.designationId,
              requiredCount: forecast.requiredCount
            });
            results.successful++;
          } catch (error) {
            results.errors.push(`Forecast ${i + 1}: ${error.message}`);
            results.failed++;
          }
        }

        return successResponse(res, {
          message: `Bulk forecast creation completed. ${results.successful} successful, ${results.failed} failed.`,
          data: results
        }, 201);

      } else {
        // SINGLE MANUAL CREATION
        const forecast = await demandForecastService.createForecast(req.body, req.tenantContext);

        return successResponse(res, {
          message: 'Demand forecast created successfully',
          data: forecast
        }, 201);
      }
    }

  } catch (error) {
    // ✅ CLEAN UP FILE ON ERROR
    if (req.file && req.file.path) {
      try {
        const fs = require('fs');
        if (fs.existsSync(req.file.path)) {
          fs.unlinkSync(req.file.path);
          console.log(`✅ Excel file deleted on error: ${req.file.path}`);
        }
      } catch (deleteError) {
        console.error(`❌ Failed to delete Excel file on error: ${req.file.path}`, deleteError);
      }
    }
    next(error);
  }
};

/**
 * Get all forecasts with filtering and pagination
 * @route GET /api/v1/demand-forecasts
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getForecasts = async (req, res, next) => {
  try {
    const result = await demandForecastService.getForecasts(req.query, req.tenantContext);

    return successResponse(res, {
      message: 'Forecasts retrieved successfully',
      data: result.forecasts,
      pagination: result.pagination
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get forecast by ID
 * @route GET /api/v1/demand-forecasts/:id
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getForecastById = async (req, res, next) => {
  try {
    const forecast = await demandForecastService.getForecastById(req.params.id, req.tenantContext);

    return successResponse(res, {
      message: 'Forecast retrieved successfully',
      data: forecast
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update forecast by ID
 * @route PUT /api/v1/demand-forecasts/:id
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const updateForecast = async (req, res, next) => {
  try {
    const forecast = await demandForecastService.updateForecast(req.params.id, req.body, req.tenantContext);

    return successResponse(res, {
      message: 'Forecast updated successfully',
      data: forecast
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Delete forecast with enhanced options
 * @route DELETE /api/v1/demand-forecasts/:id?force=true&cascade=false
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const deleteForecast = async (req, res, next) => {
  try {
    const options = {
      force: req.query.force === 'true',
      cascade: req.query.cascade !== 'false' // Default true
    };

    const result = await demandForecastService.deleteForecast(
      req.params.id,
      req.tenantContext,
      options
    );

    return successResponse(res, {
      message: result.message,
      data: {
        statistics: result.statistics
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Bulk delete forecasts
 * @route DELETE /api/v1/demand-forecasts/bulk?force=true&cascade=false
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const bulkDeleteForecasts = async (req, res, next) => {
  try {
    const { forecastIds } = req.body;
    const options = {
      force: req.query.force === 'true',
      cascade: req.query.cascade !== 'false'
    };

    if (!forecastIds || !Array.isArray(forecastIds) || forecastIds.length === 0) {
      throw new ValidationError('forecastIds array is required');
    }

    const result = await demandForecastService.bulkDeleteForecasts(
      forecastIds,
      req.tenantContext,
      options
    );

    return successResponse(res, {
      message: `Bulk delete completed. ${result.statistics.totalDeleted} successful, ${result.statistics.totalFailed} failed.`,
      data: result
    });
  } catch (error) {
    next(error);
  }
};

// Excel Upload Methods

/**
 * Generate Excel template for demand forecast upload
 * @route GET /api/v1/demand-forecasts/excel/template
 */
const generateExcelTemplate = async (req, res, next) => {
  try {
    const templateData = await demandForecastService.generateExcelTemplate(req.tenantContext);

    return successResponse(res, {
      message: 'Excel template generated successfully',
      data: templateData
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Download Excel template file
 * @route GET /api/v1/demand-forecasts/excel/download-template
 */
const downloadExcelTemplate = async (req, res, next) => {
  try {
    const templateData = await demandForecastService.generateExcelTemplate(req.tenantContext);

    // Create Excel workbook
    const XLSX = require('xlsx');
    const workbook = XLSX.utils.book_new();

    // Create worksheet with sample data
    const worksheet = XLSX.utils.json_to_sheet(templateData.templateData);

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Demand Forecasts');

    // Create instructions sheet
    const instructionsData = templateData.metadata.instructions.map((instruction, index) => ({
      'Step': index + 1,
      'Instruction': instruction
    }));

    const instructionsSheet = XLSX.utils.json_to_sheet(instructionsData);
    XLSX.utils.book_append_sheet(workbook, instructionsSheet, 'Instructions');

    // Create reference data sheets
    const departmentsSheet = XLSX.utils.json_to_sheet(templateData.metadata.departments);
    XLSX.utils.book_append_sheet(workbook, departmentsSheet, 'Departments');

    const designationsSheet = XLSX.utils.json_to_sheet(templateData.metadata.designations);
    XLSX.utils.book_append_sheet(workbook, designationsSheet, 'Designations');

    // Generate buffer
    const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

    // Set headers for file download
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=demand_forecast_template.xlsx');

    res.send(buffer);

  } catch (error) {
    next(error);
  }
};

// Helper Methods

module.exports = {
  // Basic CRUD operations
  createForecast,
  getForecasts,
  getForecastById,
  updateForecast,
  deleteForecast,
  bulkDeleteForecasts,

  // Advanced operations
  generateForecasts,
  getActiveForecasts,
  updateForecastAccuracy,
  getForecastAccuracyStats,
  getForecastRecommendations,
  applyForecastsToInstances,
  getForecastDashboard,

  // Excel template operations
  generateExcelTemplate,
  downloadExcelTemplate
};
