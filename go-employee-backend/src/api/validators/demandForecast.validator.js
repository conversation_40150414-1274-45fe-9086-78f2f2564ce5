'use strict';

/**
 * Demand Forecast Validators - PRD Implementation
 * 
 * Comprehensive validation schemas for demand forecasting
 */

const Joi = require('joi');

// Generate forecasts
const generateForecasts = Joi.object({
    departmentId: Joi.number().integer().positive().required(),
    startDate: Joi.date().iso().required(),
    endDate: Joi.date().iso().required(),
    forecastType: Joi.string().valid(
      'historical_average',
      'trend_analysis',
      'seasonal_pattern',
      'machine_learning',
      'manual_override'
    ).default('historical_average'),
    historicalPeriodDays: Joi.number().integer().min(7).max(365).default(30),
    includeSeasonality: Joi.boolean().default(true),
    includeEvents: Joi.boolean().default(false)
  }).custom((value, helpers) => {
    if (new Date(value.endDate) <= new Date(value.startDate)) {
      return helpers.error('custom.endDateAfterStart');
    }
    
    // Limit forecast period to 90 days
    const daysDiff = Math.ceil((new Date(value.endDate) - new Date(value.startDate)) / (1000 * 60 * 60 * 24));
    if (daysDiff > 90) {
      return helpers.error('custom.maxForecastPeriod');
    }
    
    return value;
  }).messages({
    'custom.endDateAfterStart': 'End date must be after start date',
    'custom.maxForecastPeriod': 'Forecast period cannot exceed 90 days'
});

// Get active forecasts
const getActiveForecasts = Joi.object({
    departmentId: Joi.number().integer().positive().optional(),
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().optional(),
    minConfidence: Joi.number().min(0).max(100).default(0),
    forecastTypes: Joi.string().optional(), // Comma-separated list
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(50)
  }).custom((value, helpers) => {
    if (value.startDate && value.endDate) {
      if (new Date(value.endDate) <= new Date(value.startDate)) {
        return helpers.error('custom.endDateAfterStart');
      }
    }
    return value;
  }).messages({
    'custom.endDateAfterStart': 'End date must be after start date'
  })


// Update forecast accuracy
const updateForecastAccuracy = Joi.object({
  actualRequirement: Joi.number().integer().min(0).required()
});

// Get forecast accuracy stats
const getForecastAccuracyStats = Joi.object({
  departmentId: Joi.number().integer().positive().optional(),
  startDate: Joi.date().iso().optional(),
  endDate: Joi.date().iso().optional(),
  forecastType: Joi.string().valid(
    'historical_average',
    'trend_analysis',
    'seasonal_pattern',
    'machine_learning',
    'manual_override'
  ).optional()
}).custom((value, helpers) => {
  if (value.startDate && value.endDate) {
    if (new Date(value.endDate) <= new Date(value.startDate)) {
      return helpers.error('custom.endDateAfterStart');
    }
  }
  return value;
}).messages({
  'custom.endDateAfterStart': 'End date must be after start date'
});

// Get forecast recommendations
const getForecastRecommendations = Joi.object({
  instanceIds: Joi.array().items(Joi.number().integer().positive()).min(1).max(100).required()
});

// Apply forecasts to instances
const applyForecastsToInstances = Joi.object({
  instanceIds: Joi.array().items(Joi.number().integer().positive()).min(1).max(100).required(),
  minConfidenceThreshold: Joi.number().min(0).max(100).default(70),
  onlyIfHigherConfidence: Joi.boolean().default(true),
  preserveManualOverrides: Joi.boolean().default(true)
});

// Get forecast dashboard
const getForecastDashboard = Joi.object({
  departmentId: Joi.number().integer().positive().optional(),
  period: Joi.string().pattern(/^\d+d$/).default('30d') // Format: "30d", "7d", etc.
});

// ==================== BASIC CRUD VALIDATORS ====================

// ✅ LEGACY SINGLE FORECAST SCHEMA - Backward compatibility
const legacyForecastSchema = Joi.object({
  departmentId: Joi.number().integer().positive().required(),
  designationId: Joi.number().integer().positive().required(),
  date: Joi.date().iso().required(),
  requiredCount: Joi.number().integer().min(0).required(),
  forecastType: Joi.string().valid(
    'manual',
    'historical',
    'seasonal',
    'predictive'
  ).default('manual'),
  priority: Joi.number().integer().min(1).max(10).default(5),
  confidence: Joi.number().min(0).max(100).default(100),
  notes: Joi.string().max(500).optional(),
  status: Joi.string().valid('active', 'inactive', 'archived').default('active')
});

// ✅ NEW DATE RANGE FORECAST SCHEMA - 3-table architecture
const newForecastSchema = Joi.object({
  name: Joi.string().min(3).max(255).required(),
  description: Joi.string().max(1000).optional(),
  departmentId: Joi.number().integer().positive().optional(),
  templateId: Joi.number().integer().positive().optional(),
  startDate: Joi.date().iso().required(),
  endDate: Joi.date().iso().required(),
  forecastType: Joi.string().valid(
    'manual',
    'template_based',
    'historical',
    'seasonal',
    'predictive'
  ).default('manual'),
  status: Joi.string().valid('draft', 'active', 'completed', 'cancelled').default('draft'),

  // ✅ NEW: Weekend and holiday exclusion options
  excludeWeekends: Joi.boolean().default(true),
  excludeHolidays: Joi.boolean().default(true),

  // ✅ NEW: Custom requirements (following schedule service pattern)
  customRequirements: Joi.array().items(
    Joi.object({
      date: Joi.date().iso().required(),
      rotaShiftId: Joi.number().integer().positive().required(),
      designationRequirements: Joi.array().items(
        Joi.object({
          designationId: Joi.number().integer().positive().required(),
          requiredCount: Joi.number().integer().min(0).required(),
          priority: Joi.number().integer().min(1).max(10).default(5),
          confidence: Joi.number().min(0).max(100).default(100),
          forecastMethod: Joi.string().valid('manual', 'historical', 'template_based', 'seasonal', 'predictive').default('manual')
        })
      ).min(1).required()
    })
  ).optional(),

  // ✅ DAILY FORECASTS ARRAY (legacy support)
  forecasts: Joi.array().items(
    Joi.object({
      date: Joi.date().iso().required(),
      forecastMethod: Joi.string().valid('manual', 'historical', 'template_based', 'seasonal', 'predictive').default('manual'),
      shifts: Joi.array().items(
        Joi.object({
          shiftName: Joi.string().max(100).default('Default'),
          startTime: Joi.string().pattern(/^([01]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/).optional(),
          endTime: Joi.string().pattern(/^([01]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/).optional(),
          confidence: Joi.number().min(0).max(100).default(100),
          designations: Joi.array().items(
            Joi.object({
              designationId: Joi.number().integer().positive().required(),
              requiredCount: Joi.number().integer().min(0).required(),
              priority: Joi.number().integer().min(1).max(10).default(5),
              confidence: Joi.number().min(0).max(100).default(100),
              forecastMethod: Joi.string().valid('manual', 'historical', 'template_based', 'seasonal', 'predictive').default('manual')
            })
          ).min(1).required(),

          // ✅ NEW: Designation overrides (following schedule service pattern)
          designationOverrides: Joi.array().items(
            Joi.object({
              designationId: Joi.number().integer().positive().required(),
              requiredCount: Joi.number().integer().min(0).required(),
              priority: Joi.number().integer().min(1).max(10).default(5),
              confidence: Joi.number().min(0).max(100).default(100),
              forecastMethod: Joi.string().valid('manual', 'historical', 'template_based', 'seasonal', 'predictive').default('manual')
            })
          ).optional()
        })
      ).min(1).default([{ shiftName: 'Default', designations: [] }])
    })
  ).optional().default([]),

  notes: Joi.string().max(1000).optional()
}).custom((value, helpers) => {
  if (new Date(value.endDate) <= new Date(value.startDate)) {
    return helpers.error('custom.endDateAfterStart');
  }

  // Limit forecast period to 90 days
  const daysDiff = Math.ceil((new Date(value.endDate) - new Date(value.startDate)) / (1000 * 60 * 60 * 24));
  if (daysDiff > 90) {
    return helpers.error('custom.maxForecastPeriod');
  }

  return value;
}).messages({
  'custom.endDateAfterStart': 'End date must be after start date',
  'custom.maxForecastPeriod': 'Forecast period cannot exceed 90 days'
});

// ✅ FLEXIBLE CREATE FORECAST VALIDATOR
// Supports: Legacy format, New format, Bulk legacy forecasts, Excel upload
const createForecast = Joi.alternatives().try(
  // ✅ NEW DATE RANGE FORMAT
  newForecastSchema,

  // ✅ LEGACY SINGLE FORECAST FORMAT - Backward compatibility
  legacyForecastSchema,

  // ✅ BULK LEGACY FORECASTS
  Joi.object({
    forecasts: Joi.array().items(legacyForecastSchema).min(1).max(100).required()
      .messages({
        'array.min': 'At least one forecast is required',
        'array.max': 'Maximum 100 forecasts allowed in bulk creation'
      })
  })
).messages({
  'alternatives.match': 'Request must contain either a new forecast format (name, startDate, endDate), legacy format (departmentId, designationId, date, requiredCount), or a forecasts array'
});

// Get forecasts (with filtering)
const getForecasts = Joi.object({
  departmentId: Joi.number().integer().positive().optional(),
  designationId: Joi.number().integer().positive().optional(),
  status: Joi.string().valid('active', 'inactive', 'archived').default('active'),
  forecastType: Joi.string().valid(
    'historical_average',
    'trend_analysis',
    'seasonal_pattern',
    'machine_learning',
    'manual_override'
  ).optional(),
  startDate: Joi.date().iso().optional(),
  endDate: Joi.date().iso().optional(),
  minConfidence: Joi.number().min(0).max(100).default(0),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(10)
});

// ✅ ENHANCED UPDATE FORECAST VALIDATOR - Supports both old and new formats
const updateForecast = Joi.object({
  // ✅ NEW FORMAT FIELDS
  name: Joi.string().min(3).max(255).optional(),
  description: Joi.string().max(1000).optional(),
  templateId: Joi.number().integer().positive().optional(),
  startDate: Joi.date().iso().optional(),
  endDate: Joi.date().iso().optional(),
  status: Joi.string().valid('draft', 'active', 'completed', 'cancelled').optional(),

  // ✅ LEGACY FORMAT FIELDS - Backward compatibility
  departmentId: Joi.number().integer().positive().optional(),
  designationId: Joi.number().integer().positive().optional(),
  date: Joi.date().iso().optional(),
  requiredCount: Joi.number().integer().min(0).optional(),

  // ✅ COMMON FIELDS
  forecastType: Joi.string().valid(
    'manual',
    'template_based',
    'historical',
    'seasonal',
    'predictive'
  ).optional(),
  confidence: Joi.number().min(0).max(100).optional(),
  notes: Joi.string().max(1000).optional(),

  // ✅ NEW: Custom requirements (following schedule service pattern)
  customRequirements: Joi.array().items(
    Joi.object({
      date: Joi.date().iso().required(),
      rotaShiftId: Joi.number().integer().positive().required(),
      designationRequirements: Joi.array().items(
        Joi.object({
          designationId: Joi.number().integer().positive().required(),
          requiredCount: Joi.number().integer().min(0).required(),
          priority: Joi.number().integer().min(1).max(10).default(5),
          confidence: Joi.number().min(0).max(100).default(100),
          forecastMethod: Joi.string().valid('manual', 'historical', 'template_based', 'seasonal', 'predictive').default('manual')
        })
      ).min(1).required()
    })
  ).optional(),

  // ✅ INSTANCE UPDATES - For updating specific dates/shifts
  forecasts: Joi.array().items(
    Joi.object({
      date: Joi.date().iso().required(),
      forecastMethod: Joi.string().valid('manual', 'historical', 'template_based', 'seasonal', 'predictive').optional(),
      shifts: Joi.array().items(
        Joi.object({
          shiftName: Joi.string().max(100).optional(),
          startTime: Joi.string().pattern(/^([01]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/).optional(),
          endTime: Joi.string().pattern(/^([01]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/).optional(),
          confidence: Joi.number().min(0).max(100).optional(),
          designations: Joi.array().items(
            Joi.object({
              designationId: Joi.number().integer().positive().required(),
              requiredCount: Joi.number().integer().min(0).required(),
              priority: Joi.number().integer().min(1).max(10).optional(),
              confidence: Joi.number().min(0).max(100).optional(),
              forecastMethod: Joi.string().valid('manual', 'historical', 'template_based', 'seasonal', 'predictive').optional()
            })
          ).optional()
        })
      ).optional()
    })
  ).optional()
}).custom((value, helpers) => {
  // Validate date range if both provided
  if (value.startDate && value.endDate) {
    if (new Date(value.endDate) <= new Date(value.startDate)) {
      return helpers.error('custom.endDateAfterStart');
    }
  }
  return value;
}).messages({
  'custom.endDateAfterStart': 'End date must be after start date'
});

// ✅ UPDATE INSTANCE DESIGNATION REQUIREMENTS (following schedule service pattern)
const updateInstanceDesignationRequirements = Joi.object({
  designationRequirements: Joi.array().items(
    Joi.object({
      designationId: Joi.number().integer().positive().required(),
      requiredCount: Joi.number().integer().min(0).max(100).required(),
      priority: Joi.number().integer().min(1).max(10).default(5),
      confidence: Joi.number().min(0).max(100).default(100),
      forecastMethod: Joi.string().valid('manual', 'historical', 'template_based', 'seasonal', 'predictive').default('manual')
    })
  ).min(1).required()
});

module.exports = {
  // Basic CRUD validators
  createForecast,
  getForecasts,
  updateForecast,

  // Advanced operation validators
  generateForecasts,
  getActiveForecasts,
  updateForecastAccuracy,
  getForecastAccuracyStats,
  getForecastRecommendations,
  applyForecastsToInstances,
  getForecastDashboard,

  // ✅ NEW: Instance designation requirements update
  updateInstanceDesignationRequirements
};
