'use strict';

/**
 * Demand Forecast Routes - PRD Implementation
 * 
 * Complete API endpoints for demand forecasting according to PRD:
 * - Forecast generation and management
 * - Historical analysis
 * - Accuracy tracking
 * - Integration with scheduling
 */

const express = require('express');
const demandForecastController = require('../controllers/demandForecast.controller');
const authenticate = require('../middlewares/authentication.middleware');
const { hasPermission } = require('../middlewares/authorization.middleware');
const { validate, validateQuery } = require('../middlewares/validation.middleware');
const { uploadSingleFile } = require('../middlewares/upload.middleware');
const demandForecastValidator = require('../validators/demandForecast.validator');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticate);

// ==================== FORECAST GENERATION ====================

/**
 * @route POST /api/v1/demand-forecasts/generate
 * @desc Generate demand forecasts for department and date range
 * @access Private
 */
router.post(
  '/generate',
  validate(demandForecastValidator.generateForecasts),
  // hasPermission('demand_forecasts:create'),
  demandForecastController.generateForecasts
);

/**
 * @route POST /api/v1/demand-forecasts/recommendations
 * @desc Get forecast recommendations for instances
 * @access Private
 */
router.post(
  '/recommendations',
  validate(demandForecastValidator.getForecastRecommendations),
  // hasPermission('demand_forecasts:read'),
  demandForecastController.getForecastRecommendations
);

/**
 * @route POST /api/v1/demand-forecasts/apply-to-instances
 * @desc Apply forecasts to shift instances
 * @access Private
 */
router.post(
  '/apply-to-instances',
  validate(demandForecastValidator.applyForecastsToInstances),
  // hasPermission('demand_forecasts:update'),
  demandForecastController.applyForecastsToInstances
);

// ==================== BASIC CRUD OPERATIONS ====================

/**
 * @route POST /api/v1/demand-forecasts
 * @desc Create demand forecasts - supports manual creation, bulk creation, and Excel upload
 * @access Private
 */
router.post(
  '/',
  uploadSingleFile('file'), 
  (req, res, next) => {
    if (req.file) {
      return next();
    }
    return validate(demandForecastValidator.createForecast)(req, res, next);
  },
  // hasPermission('demand_forecasts:create'),
  demandForecastController.createForecast
);

/**
 * @route GET /api/v1/demand-forecasts
 * @desc Get all forecasts with filtering and pagination
 * @access Private
 */
router.get(
  '/',
  validateQuery(demandForecastValidator.getForecasts),
  // hasPermission('demand_forecasts:read'),
  demandForecastController.getForecasts
);

/**
 * @route GET /api/v1/demand-forecasts/:id
 * @desc Get forecast by ID
 * @access Private
 */
router.get(
  '/:id',
  // hasPermission('demand_forecasts:read'),
  demandForecastController.getForecastById
);

/**
 * @route PUT /api/v1/demand-forecasts/:id
 * @desc Update forecast by ID
 * @access Private
 */
router.put(
  '/:id',
  validate(demandForecastValidator.updateForecast),
  // hasPermission('demand_forecasts:update'),
  demandForecastController.updateForecast
);

/**
 * @route DELETE /api/v1/demand-forecasts/:id
 * @desc Delete forecast by ID
 * @access Private
 */
router.delete(
  '/:id',
  // hasPermission('demand_forecasts:delete'),
  demandForecastController.deleteForecast
);

// ==================== FORECAST MANAGEMENT ====================

/**
 * @route GET /api/v1/demand-forecasts/active
 * @desc Get active forecasts with filtering
 * @access Private
 */
router.get(
  '/active',
  validateQuery(demandForecastValidator.getActiveForecasts),
  // hasPermission('demand_forecasts:read'),
  demandForecastController.getActiveForecasts
);

/**
 * @route GET /api/v1/demand-forecasts/dashboard
 * @desc Get forecast dashboard data
 * @access Private
 */
router.get(
  '/dashboard',
  validateQuery(demandForecastValidator.getForecastDashboard),
  // hasPermission('demand_forecasts:read'),
  demandForecastController.getForecastDashboard
);

/**
 * @route GET /api/v1/demand-forecasts/accuracy-stats
 * @desc Get forecast accuracy statistics
 * @access Private
 */
router.get(
  '/accuracy-stats',
  validateQuery(demandForecastValidator.getForecastAccuracyStats),
  // hasPermission('demand_forecasts:read'),
  demandForecastController.getForecastAccuracyStats
);

// ==================== FORECAST ACCURACY ====================

/**
 * @route PUT /api/v1/demand-forecasts/:id/accuracy
 * @desc Update forecast accuracy with actual data
 * @access Private
 */
router.put(
  '/:id/accuracy',
  
  validate(demandForecastValidator.updateForecastAccuracy),
  // hasPermission('demand_forecasts:update'),
  demandForecastController.updateForecastAccuracy
);
/**
 * @route GET /api/v1/demand-forecasts/excel/template
 * @desc Generate Excel template data for demand forecast upload
 * @access Private
 */
router.get(
  '/excel/template',
  // hasPermission('demand_forecasts:read'),
  demandForecastController.generateExcelTemplate
);

/**
 * @route GET /api/v1/demand-forecasts/excel/download-template
 * @desc Download Excel template file for demand forecast upload
 * @access Private
 */
router.get(
  '/excel/download-template',
  // hasPermission('demand_forecasts:read'),
  demandForecastController.downloadExcelTemplate
);

module.exports = router;
