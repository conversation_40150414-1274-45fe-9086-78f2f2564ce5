'use strict';

module.exports = (sequelize, DataTypes) => {
  const DemandForecast = sequelize.define('DemandForecast', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    companyId: {
      field: 'company_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'companies',
        key: 'id'
      }
    },
    businessUnitId: {
      field: 'business_unit_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'business_units',
        key: 'id'
      }
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: true,
      // comment: 'Name/title of the demand forecast'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
      // comment: 'Description of the forecast purpose'
    },
    departmentId: {
      field: 'department_id',
      type: DataTypes.INTEGER,
      allowNull: true, // Made optional like rota schedule
      references: {
        model: 'departments',
        key: 'id'
      }
    },
    templateId: {
      field: 'template_id',
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'shift_templates', // Reference to existing templates
        key: 'id'
      },
    },

    startDate: {
      field: 'start_date',
      type: DataTypes.DATEONLY,
      allowNull: false,
    },
    endDate: {
      field: 'end_date',
      type: DataTypes.DATEONLY,
      allowNull: false,
    },

    // ✅ BACKWARD COMPATIBILITY - Keep old date field for existing data
    date: {
      type: DataTypes.DATEONLY,
      allowNull: true, // Made nullable for backward compatibility
      // comment: 'Legacy single date field - use startDate/endDate for new forecasts'
    },
    // ✅ STATUS - Like rota schedule
    status: {
      type: DataTypes.ENUM('draft', 'active', 'completed', 'cancelled'),
      defaultValue: 'draft',
      // comment: 'Current status of the forecast'
    },

    // ✅ LEGACY FIELDS - Keep for backward compatibility
    designationId: {
      field: 'designation_id',
      type: DataTypes.INTEGER,
      allowNull: true, // Made nullable for new multi-designation approach
      references: {
        model: 'designations',
        key: 'id'
      }
    },
    requiredCount: {
      field: 'required_count',
      type: DataTypes.INTEGER,
      allowNull: true, // Made nullable for new instance-based approach
      defaultValue: 1,
      validate: {
        min: 0
      },
    },

    // ✅ PERFORMANCE FIELDS - Aggregated from instances
    totalInstances: {
      field: 'total_instances',
      type: DataTypes.INTEGER,
      defaultValue: 0,
      // comment: 'Total number of daily instances'
    },
    totalRequiredCount: {
      field: 'total_required_count',
      type: DataTypes.INTEGER,
      defaultValue: 0,
      // comment: 'Sum of all required counts across instances'
    },
    priority: {
      type: DataTypes.INTEGER,
      defaultValue: 5,
      validate: {
        min: 1,
        max: 10
      },
    },
    confidence: {
      type: DataTypes.DECIMAL(5, 2),
      defaultValue: 100.00,
      validate: {
        min: 0,
        max: 100
      },
    },
    forecastType: {
      field: 'forecast_type',
      type: DataTypes.ENUM('historical_average', 'trend_analysis', 'seasonal_pattern', 'machine_learning', 'manual_override','template_based'),
      defaultValue: 'historical_average',
    },
    basedOnPeriod: {
      field: 'based_on_period',
      type: DataTypes.JSON,
    },
    adjustments: {
      type: DataTypes.JSON,
    },
    metadata: {
      type: DataTypes.JSON,
    },
    notes: {
      type: DataTypes.TEXT,
    },
    status: {
      type: DataTypes.ENUM('pending', 'active','inactive', 'archived'),
      defaultValue: 'active'
    },
    validFrom: {
      field: 'valid_from',
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    validTo: {
      field: 'valid_to',
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    createdAt: {
      field: 'created_at',
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updatedAt: {
      field: 'updated_at',
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    createdById: {
      field: 'created_by_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    updatedById: {
      field: 'updated_by_id',
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    }
  }, {
    tableName: 'demand_forecasts',
    timestamps: true,
    paranoid: true,
    indexes: [
      {
        fields: ['company_id', 'business_unit_id']
      },
      {
        fields: ['business_unit_id', 'date']
      },
      {
        fields: ['date', 'status']
      },
      {
        unique: true,
        fields: ['company_id', 'business_unit_id', 'designation_id', 'date'],
        name: 'unique_forecast_per_business_unit_designation'
      }
    ]
  });

  DemandForecast.associate = function(models) {
    // Associations
    DemandForecast.belongsTo(models.Company, {
      foreignKey: 'companyId',
      as: 'company'
    });

    DemandForecast.belongsTo(models.BusinessUnit, {
      foreignKey: 'businessUnitId',
      as: 'businessUnit'
    });

    DemandForecast.belongsTo(models.Department, {
      foreignKey: 'departmentId',
      as: 'department'
    });

    DemandForecast.belongsTo(models.Designation, {
      foreignKey: 'designationId',
      as: 'designation'
    });

    DemandForecast.belongsTo(models.ShiftTemplate, {
      foreignKey: 'templateId',
      as: 'template'
    });

    DemandForecast.hasMany(models.DemandForecastInstance, {
      foreignKey: 'forecastId',
      as: 'instances'
    });

    DemandForecast.belongsTo(models.User, {
      foreignKey: 'createdById',
      as: 'createdBy'
    });

    DemandForecast.belongsTo(models.User, {
      foreignKey: 'updatedById',
      as: 'updatedBy'
    });
  };

  return DemandForecast;
};
